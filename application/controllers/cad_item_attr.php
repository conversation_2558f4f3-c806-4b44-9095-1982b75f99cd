<?php if (!defined("BASEPATH")) exit("No direct script access allowed");

/* vim: set expandtab tabstop=4 shiftwidth=4 softtabstop=4: */

/** 
 *    ___  _____________  __  ________  __
 *   / _ )/ __/ ___/ __ \/  |/  / __/ |/_/
 *  / _  / _// /__/ /_/ / /|_/ / _/_>  <  
 * /____/___/\___/\____/_/  /_/___/_/|_|
 * 
 * Controller para os atributos do cadastro de itens.
 * 
 * Controller responsavel pelas requisições e manipulações relacionadas ao
 * atributo do cadastro de item. Nele seram encontrados metodos para registro
 * e requisição dos atributos, assim como, metodos para associação com os
 * valores presentes no banco de dados.
 *  
 * PHP version 5
 *
 * LICENSE: This source file is subject to version 3.01 of the PHP license
 * that is available through the world-wide-web at the following URI:
 * http://www.php.net/license/3_01.txt.  If you did not receive a copy of
 * the PHP License and are unable to obtain it through the web, please
 * send a <NAME_EMAIL> so we can mail you a copy immediately.
 * 
 * @category  Gestão Tarifaria
 * @package   CodeIgniter
 * 
 * <AUTHOR> Santana    <<EMAIL>>
 * <AUTHOR> Dias      <<EMAIL>>
 * <AUTHOR>            <<EMAIL>>
 * <AUTHOR> Rangel <<EMAIL>>
 * 
 * @copyright 1997-2005 The PHP Group
 * @license   http://www.php.net/license/3_01.txt  PHP License 3.01
 * @see       CadItemAttr, Cad_Item_Attr::Cad_Item_Attr()
 */
class Cad_Item_Attr extends MY_Controller
{
    // {{{ Propriedades

    // }}
    // {{ __construct()

    /**
     * Metodo construtor.
     * 
     * <AUTHOR> Santana <<EMAIL>>
     * 
     * @return void          Sem retorno.
     */
    public function __construct()
    {
        parent::__construct();

        if (!is_logged()) {
            redirect("/login");
        }
    }


   public function verificarMudancasPorItem(array $dados): array
    {
        $dados_base = $dados['dados_table'];
        $dados_atuais = $dados['dbdata'];

        $mapa_base = [];
        foreach ($dados_base as $item_obj) { // Os itens são objetos
            $mapa_base[$item_obj->id_item][$item_obj->atributo] = $item_obj->codigo;
        }

        $mapa_atual = [];
        foreach ($dados_atuais as $item_arr) { // Os itens são arrays
            $mapa_atual[$item_arr['id_item']][$item_arr['atributo']] = $item_arr['codigo'];
        }

        $todos_os_ids = array_keys($mapa_base + $mapa_atual);
        
        $resultados_por_item = [];
        foreach ($todos_os_ids as $id_item) {
            $resultados_por_item[$id_item] = ['houve_mudanca' => false];

            $atributos_base = $mapa_base[$id_item] ?? [];
            $atributos_atuais = $mapa_atual[$id_item] ?? [];
            $todos_os_atributos = array_keys($atributos_base + $atributos_atuais);

            foreach ($todos_os_atributos as $atributo) {

                $valor_base = $atributos_base[$atributo] ?? null;
                $valor_atual = $atributos_atuais[$atributo] ?? null;

                if ($valor_base != $valor_atual) {
                    $resultados_por_item[$id_item]['houve_mudanca'] = true;
                    break; 
                }
            }
        }

        return $resultados_por_item;
    }

    // {{{ Registros

    // }}
    // {{ ajax_save_attr()

    /**
     * Chamada AJAX responsavel por salvar um atributo na tabela.
     * 
     * <AUTHOR> Santana  <<EMAIL>>
     * 
     * @throws \Exception     Se ocorrer algum erro durante a execução.
     * @return \json_response
     */
    public function ajax_save_attr()
    {
        $id_usuario = sess_user_id();
        try {
            $this->load->model([
                "cad_item_model",
                "cad_item_attr_model",
                "item_model",
                "log_wf_atributos_model",
                "cad_item_wf_atributo_model"
            ]);

            $this->load->model(array('cad_item_model'));
            $this->cad_item_model->_attr_default_item = [];
            $post = $this->get_post_values();

            $attr = $post["attr"];
            if (empty($attr)) {
                throw new \Exception("Atributo vazio ou não definido.");
            }
            
            $id_item = $post["id_item"];
            $atributo_pai = $attr["codigo"];

            $item = $this->cad_item_model->get_entry($id_item);

            if(empty($item->id_grupo_tarifario)) {
                return response_json([
                    "msg"    => "Não foi possível atualizar os dados. O item não possui grupo tarifário definido.",
                    "status" => false,
                    "data"   => []
                ], 400);
            }

            if(empty($item)) {
                throw new \Exception("Item vazio ou não definido.");
            }

            $item = \json_decode(\json_encode($item), TRUE); // Força a conversão para array.

            $value_has_changed = $this->cad_item_attr_model->value_has_changed($attr["dbdata"], "codigo"); // Verifica se o atributo principal foi alterado.
    
            $subattrs = isset($attr["listaSubatributos"]) ? TRUE : FALSE;
            $dbdata = [];
            // if ($value_has_changed || $subattrs) {
            //        // $this->cad_item_attr_model->delete_childs($id_item, $atributo_pai); // Deleta os atributos filhos.
            // }


            $this->db->where('id_item', $id_item);
            $query = $this->db->get('cad_item_attr');
            $dados_table  = $query->result();

            $this->cad_item_attr_model->save_attr($attr, $item, [], TRUE, $post);
            
            $dbdata[] = $attr['dbdata'];

            foreach ($attr['condicionados'] as $condicionado)
            {
                if (!empty($condicionado['atributo']['dbdata']['codigo']))
                {
                    $dbdata[] = $condicionado['atributo']['dbdata'];
                }
            }
   
            $resultado = $this->verificarMudancasPorItem([ 'dbdata' => $dbdata, 'dados_table' => $dados_table]);
            
            if ($resultado['houve_mudanca'] && $item['status_wf'] == 'homologados')
            {
                $this->cad_item_wf_atributo_model->set_status('em_revisao', $id_item);
                $this->log_wf_atributos_model->registrar_log(
                    $id_item,
                    null,
                    null,
                    null,
                    5,
                    'movimentacao_manual',
                    $id_usuario,
                    null
                );
            }

            return response_json([
                "msg"    => "Atributo <strong>" . $attr["codigo"] . "</strong> atualizado.",
                "status" => TRUE,
                "data"   => $attr
            ], 200);

        } catch (\Exception $e) {
            return response_json([
                "msg"    => $e->getMessage(),
                "status" => FALSE
            ], 400);
        }
    }

    // }}
    // {{ get_post_values()

    /**
     * Captura a chamada POST do PHP.
     * 
     * <AUTHOR> Santana  <<EMAIL>>
     * <AUTHOR>          <<EMAIL>>
     * 
     * @throws \Exception     Se ocorrer algum erro durante a execução.
     * @return mixed          Array com os dados POST.
     */
    private function get_post_values()
    {
        $post = \json_decode(file_get_contents("php://input"), TRUE);

        if (\json_last_error()) {
            throw new \Exception(\json_last_error_msg());
        }

        return $post;
    }

    // {{{ Requisições

    // }}
    // {{ ajax_get_attrs()

    /**
     * Chamada AJAX responsavel por requerir os atributos relacionados
     * ao NCM do item.
     * && !$('#tab_attrs').find('.vincular-complete-sign').hasClass('done')
     * <AUTHOR> Santana  <<EMAIL>>
     * <AUTHOR>          <<EMAIL>>
     * >
     * @return \json_response O item correspondente ao NCM.
     */
    public function ajax_get_attrs()
    {
        try {
            $this->load->model([
                "catalogo/produto_model",
                "grupo_tarifario_attr_model",
                "cad_item_attr_model",
            ]);

            $ncm = $this->input->get("ncm"); // O NCM em questão.

            $id_grupo_tarifario = $this->input->get("id_grupo_tarifario"); // O ID do grupo tarifario em questão.
            $id_item            = $this->input->get("id_item");            // O ID do item em questão.

            $ncm_item = $this->get_required_attrs($this->produto_model->get_attr_ncm($ncm));

            if (empty($ncm_item['listaAtributos'])) {
                throw new \Exception("Nenhum atributo encontrado.");
            }

            if (!empty($id_grupo_tarifario)) {
                $ncm_item["defaultAttrs"] = $this->grupo_tarifario_attr_model->check_attr_default_ncm($id_grupo_tarifario);

            } else if (!empty($id_item)) {
                $ncm_item["defaultAttrs"] = $this->cad_item_attr_model->get_attr($id_item);
            
            } else {
                throw new \Exception("Parametros invalidos ou nao informados.");
            }
            
            $ncm_item["assocAttrs"] = $this->create_assoc_attrs_structure($ncm_item);

            return response_json([
                "msg"     => "Sucesso",
                "success" => TRUE,
                "data"    => $ncm_item
            ], 200);

        } catch (\Exception $e) {
            return response_json([
                "msg"     => $e->getMessage(),
                "success" => FALSE,
            ], 400);
        }
    }

    // }}
    // {{ get_required_attrs()

    /**
     * Realiza uma filtragem dos atributos recebidos do banco pelos obrigatorios.
     * 
     * <AUTHOR> Santana              <<EMAIL>>
     * <AUTHOR> Rangel           <<EMAIL>>
     * 
     * @param  object|array     $ncm_item Os atributos do JSON
     * 
     * @return array                      Os dados do NCM filtrados.
     */
    private function get_required_attrs($listaAtributos)
    {
        if (empty($listaAtributos)) {
            throw new \Exception("Item NCM nao informado ou invalido");
        }

        $listaAtributos = \json_decode(\json_encode($listaAtributos), TRUE); // Força a conversão para array.

        $ncm_item = [];
        $ncm_item['listaAtributos'] = $listaAtributos;

        // foreach ($ncm_item["listaAtributos"] as $k => $attr) {
        //     $ncm_regra = FALSE;
            
        //     foreach ($attr["objetivos"] as $objetivo) {
        //         if ($objetivo["codigo"] == 7 && $attr["modalidade"] == "Importação") {
        //             $ncm_regra = TRUE;
        //         }
        //     }
            
        //     if ($ncm_regra == FALSE) {
        //         unset($ncm_item["listaAtributos"][$k]); // Remove os atributos que não satisfazem a regra.
        //     }
        // }
        
        \array_multisort($ncm_item["listaAtributos"]);

        return $ncm_item;
    }

    // }}
    // {{ create_assoc_attrs_structure()

    /**
     * Cria uma estrutura com associação entre os valores no banco e os atributos
     * do item do NCM.
     * 
     * <AUTHOR> Santana           <<EMAIL>>
     * <AUTHOR> Dias             <<EMAIL>>
     * 
     * @param  object|array  $ncm_item O item NCM.
     * 
     * @return array                   O array com a associação.
     */
    private function create_assoc_attrs_structure($ncm_item)
    {
        if (empty($ncm_item)) {
            throw new \Exception("Item NCM nao informado ou invalido");
        }

        $ncm_item = \json_decode(\json_encode($ncm_item), TRUE); // Força a conversão para array.

        $arr_dbdata = $ncm_item["defaultAttrs"];   // Os dados do banco.
        $arr_attr   = $ncm_item["listaAtributos"]; // Os atributos do JSON.

        $this->assoc_recursively($arr_dbdata, $arr_attr); // Executa a associação.

        return $arr_attr;
    }

    // }}
    // {{ assoc_recursively()

    /**
     * Itera os valores dos atributos para realizar a associação com os valores no banco.
     * 
     * <AUTHOR> Santana              <<EMAIL>>
     * <AUTHOR> Dias                <<EMAIL>>
     * 
     * @param  array         $arr_dbdata  Os dados do banco.
     * @param  array         $arr_attr    A lista de atributos do JSON.
     * @param  array         $parent_attr O atributo pai em tempo de execução. Pode ser
     *                                    nulo para o primeiro atributo.
     * 
     * @throws \Exception                 Se os dados necessarios não forem informados.
     * @return bool                       Se a associação for bem-sucedida.
     */
    private function assoc_recursively($arr_dbdata, &$arr_attr, $parent_attr = NULL)
    {
        if (empty($arr_attr)) {
            throw new \Exception("Dados invalidos para associacao");
        }

        if (empty($arr_dbdata)) {
            $arr_dbdata = [];
        }

        foreach($arr_attr as &$attr) {
            $attr_template = !empty($attr["atributo"])? $attr["atributo"] : $attr; // Cria o template com base no atributo alvo.

            $attr_template["dbdata"] = [ "codigo" => "" ]; // Por padrão adiciona a propiedade dbdata e codigo.

            foreach($arr_dbdata as $dbdata) {
                if (
                    !empty($parent_attr)
                    && !empty($dbdata["atributo_pai"])
                    && $dbdata["atributo_pai"] == $parent_attr["codigo"]
                    && $dbdata["atributo"] == $attr_template["codigo"]
                ) {
                    $attr_template["dbdata"] = $dbdata;

                } else if($dbdata["atributo"] == $attr_template["codigo"]) {
                    $attr_template["dbdata"] = $dbdata;
                }
            }

            if ($attr_template["atributoCondicionante"] && !empty($attr_template["condicionados"])) {
                $this->assoc_recursively($arr_dbdata, $attr_template["condicionados"], $attr_template);

            } else if (\strtoupper($attr_template["formaPreenchimento"]) == "COMPOSTO" && !empty($attr_template["listaSubatributos"])) {
                $this->assoc_recursively($arr_dbdata, $attr_template["listaSubatributos"], $attr_template);
            }

            if (!empty($attr["atributo"])) {
                $attr["atributo"] = $attr_template;

            } else {
                $attr = $attr_template;
            }
        }
    }
}
